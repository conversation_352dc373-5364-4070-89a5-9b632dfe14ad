# BaseConfig Store 使用指南

## 概述

BaseConfig Store 模块用于管理基础配置数据，包括枚举数据、地理位置数据、用户信息等。本指南重点介绍地理位置数据的使用方法。

## Store 结构

### State
```javascript
{
  projectId: 1,                    // 选择的项目ID
  enumerations: {},                // 枚举类数据
  geographicalData: null,          // 地理位置数据（省市区三级嵌套结构）
  token: null,                     // 用户token
  userInfo: null                   // 用户信息
}
```

### 地理位置数据格式
```javascript
[
  {
    "children": [
      {
        "children": [...],           // 区县列表
        "modelLabel": "city",
        "code": 110100,
        "name": "北京市"
      }
    ],
    "modelLabel": "province",
    "code": 110000,
    "name": "北京市"
  }
]
```

## 使用方法

### 1. 在组件中引入

```javascript
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  computed: {
    ...mapState('baseConfig', ['geographicalData']),
    ...mapGetters('baseConfig', [
      'provinces',
      'getCitiesByProvince',
      'getDistrictsByCity',
      'getAreaNameByCode'
    ])
  },
  methods: {
    ...mapActions('baseConfig', ['setGeographicalData'])
  }
}
```

### 2. 加载地理数据

```javascript
// 在组件创建时加载数据
async created() {
  await this.setGeographicalData();
}

// 强制刷新数据
async refreshData() {
  await this.setGeographicalData(true);
}
```

### 3. 获取省份列表

```javascript
computed: {
  ...mapGetters('baseConfig', ['provinces'])
}

// 在模板中使用
<el-select v-model="selectedProvince">
  <el-option
    v-for="province in provinces"
    :key="province.code"
    :label="province.name"
    :value="province.code"
  />
</el-select>
```

### 4. 获取城市列表

```javascript
computed: {
  ...mapGetters('baseConfig', ['getCitiesByProvince']),
  cities() {
    return this.selectedProvince ? this.getCitiesByProvince(this.selectedProvince) : [];
  }
}
```

### 5. 获取区县列表

```javascript
computed: {
  ...mapGetters('baseConfig', ['getDistrictsByCity']),
  districts() {
    return this.selectedCity ? this.getDistrictsByCity(this.selectedCity) : [];
  }
}
```

### 6. 根据编码获取名称

```javascript
computed: {
  ...mapGetters('baseConfig', ['getAreaNameByCode']),
  provinceName() {
    return this.getAreaNameByCode(this.selectedProvince);
  }
}
```

## 完整示例

### 三级联动选择器

```vue
<template>
  <el-cascader
    v-model="selectedArea"
    :options="cascaderOptions"
    :props="cascaderProps"
    placeholder="请选择省市区"
  />
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      selectedArea: [],
      cascaderProps: {
        value: 'code',
        label: 'name',
        children: 'children'
      }
    };
  },
  computed: {
    ...mapGetters('baseConfig', ['provinces']),
    cascaderOptions() {
      return this.provinces.map(province => ({
        code: province.code,
        name: province.name,
        children: (province.children || []).map(city => ({
          code: city.code,
          name: city.name,
          children: (city.children || []).map(district => ({
            code: district.code,
            name: district.name
          }))
        }))
      }));
    }
  }
};
</script>
```

### 分级选择器

```vue
<template>
  <div>
    <!-- 省份选择 -->
    <el-select v-model="selectedProvince" @change="handleProvinceChange">
      <el-option
        v-for="province in provinces"
        :key="province.code"
        :label="province.name"
        :value="province.code"
      />
    </el-select>
    
    <!-- 城市选择 -->
    <el-select v-model="selectedCity" @change="handleCityChange" :disabled="!selectedProvince">
      <el-option
        v-for="city in cities"
        :key="city.code"
        :label="city.name"
        :value="city.code"
      />
    </el-select>
    
    <!-- 区县选择 -->
    <el-select v-model="selectedDistrict" :disabled="!selectedCity">
      <el-option
        v-for="district in districts"
        :key="district.code"
        :label="district.name"
        :value="district.code"
      />
    </el-select>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      selectedProvince: '',
      selectedCity: '',
      selectedDistrict: ''
    };
  },
  computed: {
    ...mapGetters('baseConfig', ['provinces', 'getCitiesByProvince', 'getDistrictsByCity']),
    cities() {
      return this.selectedProvince ? this.getCitiesByProvince(this.selectedProvince) : [];
    },
    districts() {
      return this.selectedCity ? this.getDistrictsByCity(this.selectedCity) : [];
    }
  },
  methods: {
    handleProvinceChange() {
      this.selectedCity = '';
      this.selectedDistrict = '';
    },
    handleCityChange() {
      this.selectedDistrict = '';
    }
  }
};
</script>
```

## API 参考

### Actions

- `setGeographicalData(forceRefresh = false)` - 获取并设置地理位置数据

### Getters

- `provinces` - 获取所有省份列表
- `getCitiesByProvince(provinceCode)` - 根据省份编码获取城市列表
- `getDistrictsByCity(cityCode)` - 根据城市编码获取区县列表
- `getAreaNameByCode(code)` - 根据编码获取地区名称

## 注意事项

1. 地理数据具有缓存机制，默认缓存30分钟
2. 使用 `forceRefresh = true` 可以强制刷新缓存
3. 所有getter都会自动处理数据为空的情况
4. 建议在应用启动时预加载地理数据以提升用户体验

import { getGeographicalData } from "@/api/base-config";

const state = {
  // 地理位置数据
  geographicalData: [],
};

const mutations = {
  setGeographicalData(state, val) {
    state.geographicalData = val;
  }
};

const actions = {
  async setGeographicalData({ commit }, forceRefresh = false) {
    try {
      const res = await getGeographicalData(forceRefresh);
      console.log(22228889);

      if (res.code === 0) {
        commit("setGeographicalData", res.data || []);
        console.log(222);

      }
      return res;
    } catch (error) {
      console.error("获取地理位置数据失败:", error);
      commit("setGeographicalData", []);
      throw error;
    }
  }
};



export default {
  namespaced: true,
  state,
  mutations,
  actions,
};

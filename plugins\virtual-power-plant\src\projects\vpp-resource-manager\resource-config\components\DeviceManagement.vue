<template>
  <div class="device-management-container">
    <div class="device-management">
      <!-- 设备表格 -->
      <el-table
        class="flex1"
        :data="filteredDevices"
        height="true"
        border
        highlight-current-row
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="60" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" />
        <el-table-column prop="deviceType" label="设备类型" />
        <el-table-column prop="ratedPower" label="额定功率（kW）" />
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click="handleDetail(scope.row)"
            >
              详情
            </span>
            <span class="action-link edit-link" @click="handleEdit(scope.row)">
              编辑
            </span>
            <span
              class="action-link delete-link"
              @click="handleDelete(scope.row)"
            >
              删除
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增设备弹窗 -->
    <el-dialog
      :title="isEditMode ? $T('编辑设备') : $T('新增设备')"
      :visible.sync="addDeviceDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="add-device-dialog"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <el-form
          ref="addDeviceForm"
          :model="addDeviceFormData"
          :rules="addDeviceFormRules"
          label-position="top"
          class="device-form"
        >
          <el-row :gutter="30">
            <el-col :span="12">
              <!-- 设备名称 -->
              <el-form-item :label="$T('设备名称')" prop="deviceName" required>
                <el-input
                  v-model="addDeviceFormData.deviceName"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 设备类型 -->
              <el-form-item :label="$T('设备类型')" prop="deviceType" required>
                <el-select
                  v-model="addDeviceFormData.deviceType"
                  :placeholder="$T('请选择')"
                  style="width: 100%"
                  :disabled="isEditMode"
                  @change="handleDeviceTypeChange"
                >
                  <el-option
                    v-for="item in deviceTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <!-- 管网设备 -->
              <el-form-item
                :label="$T('管网设备')"
                prop="networkDevice"
                required
              >
                <div class="network-device-input">
                  <el-input
                    v-model="addDeviceFormData.networkDevice"
                    :placeholder="$T('请选择')"
                    readonly
                  />
                  <i
                    class="el-icon-edit network-device-icon"
                    @click="selectNetworkDevice"
                  ></i>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button plain @click="handleCancel">{{ $T("取消") }}</el-button>
        <el-button type="success" @click="handleConfirm">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 管网设备选择弹窗 -->
    <el-dialog
      :title="$T('选择管网设备')"
      :visible.sync="networkDeviceDialogVisible"
      width="800px"
      :append-to-body="true"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="network-device-dialog"
    >
      <div class="network-device-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="networkDeviceSearchKeyword"
            :placeholder="$T('请输入设备名称')"
            class="search-input"
            clearable
            @input="handleNetworkDeviceSearch"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>

        <!-- 管网设备列表 -->
        <el-table
          :data="networkDevices"
          :loading="networkDeviceLoading"
          style="width: 100%; margin-top: 16px"
          height="400"
          @selection-change="handleNetworkDeviceSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="deviceName" :label="$T('设备名称')" />
          <el-table-column prop="deviceType" :label="$T('设备类型')" />
          <el-table-column prop="id" :label="$T('设备ID')" />
          <el-table-column prop="roomType" :label="$T('房间类型')" />
        </el-table>

        <!-- 分页 -->
        <!-- <div class="pagination-container" v-if="networkDeviceTotal > 10">
          <el-pagination
            @size-change="handleNetworkDeviceSizeChange"
            @current-change="handleNetworkDeviceCurrentChange"
            :current-page="networkDeviceCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="networkDevicePageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="networkDeviceTotal"
          />
        </div> -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="networkDeviceDialogVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button
          type="primary"
          @click="confirmNetworkDeviceSelection"
          :disabled="selectedNetworkDevices.length === 0"
        >
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 设备详情抽屉 -->
    <el-drawer
      :title="$T('详情')"
      :visible.sync="deviceDetailDrawerVisible"
      direction="rtl"
      size="60%"
      class="device-detail-drawer"
      @close="handleDetailDrawerClose"
    >
      <div class="detail-content" v-if="currentDeviceDetail">
        <!-- 设备基本信息 -->
        <div class="basic-info-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">{{ $T("设备名称") }}</div>
              <div class="info-value">
                {{ currentDeviceDetail.deviceName || "--" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ $T("设备类型") }}</div>
              <div class="info-value">
                {{
                  getDeviceTypeNameById(currentDeviceDetail.deviceType) || "--"
                }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">{{ $T("额定功率") }} (kW)</div>
              <div class="info-value">
                {{ currentDeviceDetail.ratedPower || "--" }}
              </div>
            </div>
          </div>
        </div>

        <!-- 管网设备列表 -->
        <div class="device-list-section">
          <div class="list-title">{{ $T("管网设备列表") }}</div>
          <div class="device-list-table">
            <div class="table-header">
              <div class="header-cell serial-cell">{{ $T("序号") }}</div>
              <div class="header-cell name-cell">{{ $T("管网设备名称") }}</div>
              <div class="header-cell type-cell">{{ $T("设备类型") }}</div>
            </div>
            <div
              class="table-row"
              v-for="(device, index) in networkDeviceList"
              :key="index"
            >
              <div class="table-cell serial-cell">{{ device.id }}</div>
              <div class="table-cell name-cell">{{ device.name }}</div>
              <div class="table-cell type-cell">
                {{ device.modelLabelChinese }}
              </div>
            </div>
            <!-- 空状态显示 -->
            <div v-if="networkDeviceList.length === 0" class="empty-state">
              <div class="empty-text">{{ $T("暂无关联的管网设备") }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
// 导入API和组件
import {
  getDevicePage,
  createDevice,
  getDeviceById,
  updateDevice,
  deleteDevice,
  batchDeleteDevices
} from "@/api/device-management";
import {
  getMonitorDevicesByRoom,
  getDeviceMonitorRelations
} from "@/api/base-config";
import { getSiteById } from "@/api/site-management";
import { processDeviceData } from "@/utils/deviceTypeUtils";
import { getEnumOptions, getEnumLabel } from "@/utils/enumManager";

export default {
  name: "DeviceManagement",
  props: {
    siteId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedDevices: [], // 选中的设备列表
      devices: [], // 设备列表数据
      loading: false,
      tableLoading: false,
      searchTimer: null,

      // 新增设备弹窗相关
      addDeviceDialogVisible: false, // 弹窗显示状态
      isEditMode: false, // 是否为编辑模式
      currentEditDeviceId: null, // 当前编辑的设备ID

      // 设备详情抽屉相关
      deviceDetailDrawerVisible: false, // 抽屉显示状态
      currentDeviceDetail: null, // 当前查看的设备详情
      networkDeviceList: [], // 关联的管网设备列表

      // 管网设备选择相关
      networkDeviceDialogVisible: false,
      networkDevices: [],
      networkDeviceLoading: false,
      networkDeviceSearchKeyword: "",
      networkDeviceCurrentPage: 1,
      networkDevicePageSize: 10,
      networkDeviceTotal: 0,
      selectedNetworkDevices: [],
      networkDeviceSearchTimer: null, // 管网设备搜索防抖定时器
      siteInfo: null, // 站点信息，包含房间信息
      deviceMonitorRelations: [], // 电厂设备与管网设备关联关系
      allowedNetworkDeviceTypes: [], // 当前设备类型允许关联的管网设备类型

      // 设备类型选项（从枚举动态加载）
      deviceTypeOptions: [],

      // 新增设备表单数据
      addDeviceFormData: {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "", // 管网设备ID
        networkDeviceModelLabel: "", // 管网设备类型标识
        ratedPower: ""
      },

      // 新增设备表单验证规则
      addDeviceFormRules: {
        deviceName: [
          {
            required: true,
            message: "请输入设备名称",
            trigger: "blur"
          },
          {
            max: 50,
            message: "设备名称不能超过50个字符",
            trigger: "blur"
          }
        ],
        deviceType: [
          {
            required: true,
            message: "请选择设备类型",
            trigger: "change"
          }
        ],
        networkDevice: [
          {
            required: true,
            message: "请选择管网设备",
            trigger: "change"
          }
        ],
        ratedPower: [
          {
            type: "number",
            message: "额定功率必须为数字",
            trigger: "blur"
          }
        ]
      }
    };
  },
  mounted() {
    this.loadDevices();
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedCategory: {
      handler() {
        this.currentPage = 1;
        this.loadDevices();
      }
    },
    siteId: {
      handler() {
        if (this.siteId) {
          this.loadDevices();
        }
      },
      immediate: true
    }
  },
  computed: {
    filteredDevices() {
      // 如果是按站点加载，使用客户端过滤
      if (this.siteId) {
        let filtered = this.devices;

        // 按设备名称搜索
        if (this.searchKeyword) {
          filtered = filtered.filter(device =>
            device.deviceName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          );
        }

        // 按设备类型筛选
        if (this.selectedCategory) {
          filtered = filtered.filter(
            device => device.deviceType === this.selectedCategory
          );
        }

        // 分页
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      } else {
        // 服务端分页，直接返回设备列表
        return this.devices;
      }
    },
    totalDevices() {
      return this.siteId ? this.devices.length : this.total;
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadDevices();
      }, 500);
    },

    // 数据转换：将API返回的设备数据转换为组件显示格式
    transformDeviceData(apiDevice) {
      return {
        id: apiDevice.id,
        deviceName: apiDevice.deviceName,
        deviceType: this.getDeviceTypeNameById(apiDevice.deviceType), // 转换为中文名称显示
        deviceSubtype: apiDevice.deviceSubType,
        deviceStatus: apiDevice.deviceStatus,
        ratedPower: apiDevice.ratedPower,
        deviceId: apiDevice.deviceId,
        siteId: apiDevice.siteId,
        manufacturer: apiDevice.manufacturer,
        model: apiDevice.model,
        maxWorkingPower: apiDevice.maxWorkingPower,
        minWorkingPower: apiDevice.minWorkingPower,
        position: apiDevice.position,
        installationDate: apiDevice.installationDate,
        operationDate: apiDevice.operationDate,
        createTime: apiDevice.createTime,
        updateTime: apiDevice.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiDevice
      };
    },

    // 加载设备列表
    async loadDevices() {
      this.tableLoading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索条件
        if (this.searchKeyword) {
          params.deviceName = this.searchKeyword;
        }
        if (this.selectedCategory) {
          // selectedCategory现在已经是数字ID了，直接使用
          params.deviceType = this.selectedCategory;
        }
        if (this.siteId) {
          params.siteId = this.siteId;
        }

        const response = await getDevicePage(params);

        if (response.code === 0) {
          this.devices = response.data.records.map(device =>
            this.transformDeviceData(device)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;
        } else {
          this.$message.error(response.msg || "加载设备列表失败");
        }
      } catch (error) {
        console.error("加载设备列表失败:", error);
        this.$message.error("加载设备列表失败");
      } finally {
        this.tableLoading = false;
      }
    },

    // 打开新增设备弹窗
    handleAdd() {
      // 重置表单和编辑模式
      this.resetAddDeviceForm();
      this.isEditMode = false;
      this.currentEditDeviceId = null;
      // 打开弹窗
      this.addDeviceDialogVisible = true;
    },

    // 弹窗关闭处理
    handleDialogClose() {
      console.log("设备弹窗已关闭");
      // 重置表单和编辑模式
      this.resetAddDeviceForm();
      this.isEditMode = false;
      this.currentEditDeviceId = null;
    },

    // 取消按钮处理
    handleCancel() {
      this.addDeviceDialogVisible = false;
      this.isEditMode = false;
      this.currentEditDeviceId = null;
    },

    // 确定按钮处理
    handleConfirm() {
      // 表单验证
      this.$refs.addDeviceForm.validate(valid => {
        if (valid) {
          // 设备类型现在已经是数字ID了，不需要转换
          const deviceTypeId = this.addDeviceFormData.deviceType;

          // 构造提交数据
          const deviceData = {
            deviceName: this.addDeviceFormData.deviceName.trim(),
            deviceType: deviceTypeId || this.addDeviceFormData.deviceType, // 使用设备类型枚举ID
            siteId: this.siteId, // 添加站点ID
            ratedPower: this.addDeviceFormData.ratedPower || 0
          };

          // 如果选择了管网设备，添加管网设备关联信息
          if (this.addDeviceFormData.networkDeviceId) {
            deviceData.monitorDeviceRelations = [
              {
                id: this.addDeviceFormData.networkDeviceId,
                name: this.addDeviceFormData.networkDevice,
                modelLabel: this.addDeviceFormData.networkDeviceModelLabel
              }
            ];
          }

          console.log("构造的设备数据:", deviceData);

          // 调用API保存设备数据
          this.saveDevice(deviceData);
        } else {
          this.$message.error("请检查输入信息");
        }
      });
    },

    // 保存设备数据
    async saveDevice(deviceData) {
      try {
        console.log("保存设备数据:", deviceData);

        let res;
        if (this.isEditMode) {
          // 编辑模式：调用更新API
          res = await updateDevice(this.currentEditDeviceId, deviceData);
        } else {
          // 新增模式：调用创建API
          res = await createDevice(deviceData);
        }

        if (res.code === 0) {
          this.$message.success(
            this.isEditMode ? "编辑设备成功" : "新增设备成功"
          );
          this.addDeviceDialogVisible = false;
          this.isEditMode = false;
          this.currentEditDeviceId = null;
          this.loadDevices(); // 重新加载设备列表

          // 通知父组件刷新树形结构
          this.$emit("refresh-tree");
        } else {
          this.$message.error(
            res.msg || (this.isEditMode ? "编辑设备失败" : "新增设备失败")
          );
        }
      } catch (error) {
        console.error("保存设备失败:", error);
        this.$message.error(this.isEditMode ? "编辑设备失败" : "新增设备失败");
      }
    },

    // 选择管网设备
    async selectNetworkDevice() {
      try {
        // 检查是否已选择设备类型
        if (!this.addDeviceFormData.deviceType) {
          this.$message.warning(this.$T("请先选择设备类型"));
          return;
        }

        // 检查是否有允许关联的管网设备类型
        if (!this.allowedNetworkDeviceTypes.length) {
          this.$message.warning(this.$T("当前设备类型无可关联的管网设备类型"));
          return;
        }

        // 首先获取站点信息
        if (!this.siteInfo && this.siteId) {
          await this.loadSiteInfo();
        }

        if (!this.siteInfo || !this.siteInfo.roomId) {
          this.$message.warning(
            this.$T("当前站点未关联房间，无法查询管网设备")
          );
          return;
        }

        // 打开管网设备选择弹窗
        this.networkDeviceDialogVisible = true;
        // 加载管网设备列表
        await this.loadNetworkDevices();
      } catch (error) {
        console.error("打开管网设备选择弹窗失败:", error);
        this.$message.error(this.$T("打开管网设备选择弹窗失败"));
      }
    },

    // 重置新增设备表单
    resetAddDeviceForm() {
      this.addDeviceFormData = {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "",
        networkDeviceModelLabel: "",
        ratedPower: ""
      };

      // 清空管网设备选择
      this.selectedNetworkDevices = [];
      this.allowedNetworkDeviceTypes = [];

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.addDeviceForm) {
          this.$refs.addDeviceForm.clearValidate();
        }
      });
    },

    // 查看设备详情
    async handleDetail(row) {
      console.log("查看设备详情:", row);

      try {
        // 显示抽屉并清空之前的数据
        this.deviceDetailDrawerVisible = true;
        this.currentDeviceDetail = null;

        // 调用API获取设备详情
        const response = await getDeviceById(row.id);

        if (response.code === 0) {
          // 设置设备详情数据
          this.currentDeviceDetail = response.data;

          // 处理关联的管网设备列表
          if (
            response.data.monitor_device_relations &&
            response.data.monitor_device_relations.length > 0
          ) {
            this.networkDeviceList = response.data.monitor_device_relations.map(
              (device, index) => ({
                id: String(index + 1).padStart(2, "0"), // 序号
                name: device.name || "--",
                deviceId: device.deviceId,
                modelLabel: device.modellabel,
                modelLabelChinese: this.getNetworkDeviceTypeName(
                  device.modellabel
                ), // 转换为中文
                rate: device.rate
              })
            );
          } else {
            // 如果没有关联的管网设备，显示空状态
            this.networkDeviceList = [];
          }

          console.log("获取设备详情成功:", this.currentDeviceDetail);
          console.log("关联的管网设备:", this.networkDeviceList);
        } else {
          this.$message.error(response.msg || this.$T("获取设备详情失败"));
          this.deviceDetailDrawerVisible = false;
        }
      } catch (error) {
        console.error("获取设备详情失败:", error);
        this.$message.error(this.$T("获取设备详情失败"));
        this.deviceDetailDrawerVisible = false;
      }
    },

    // 详情抽屉关闭处理
    handleDetailDrawerClose() {
      this.currentDeviceDetail = null;
    },

    async handleEdit(row) {
      console.log("编辑设备:", row);

      try {
        // 设置编辑模式
        this.isEditMode = true;
        this.currentEditDeviceId = row.id;

        // 调用API获取设备详情
        const response = await getDeviceById(row.id);

        if (response.code === 0) {
          const deviceData = response.data;

          // 预填充表单数据
          this.addDeviceFormData = {
            deviceName: deviceData.deviceName || "",
            deviceType: deviceData.deviceType || "",
            networkDevice: "",
            networkDeviceId: "",
            networkDeviceModelLabel: "",
            ratedPower: deviceData.ratedPower || ""
          };

          // 处理关联的管网设备信息
          if (
            deviceData.monitor_device_relations &&
            deviceData.monitor_device_relations.length > 0
          ) {
            const networkDevice = deviceData.monitor_device_relations[0];
            this.addDeviceFormData.networkDevice = networkDevice.name || "";
            this.addDeviceFormData.networkDeviceId =
              networkDevice.deviceId || "";
            this.addDeviceFormData.networkDeviceModelLabel =
              networkDevice.modellabel || "";
          }

          // 更新允许的管网设备类型
          if (deviceData.deviceType) {
            this.updateAllowedNetworkDeviceTypes(deviceData.deviceType);
          }

          // 显示弹窗
          this.addDeviceDialogVisible = true;

          console.log("编辑设备数据预填充完成:", this.addDeviceFormData);
        } else {
          this.$message.error(response.msg || this.$T("获取设备详情失败"));
        }
      } catch (error) {
        console.error("获取设备详情失败:", error);
        this.$message.error(this.$T("获取设备详情失败"));
      }
    },

    handleDelete(row) {
      console.log("删除设备:", row);

      // 弹出删除确认对话框
      this.$confirm(
        this.$T("确定要删除设备「{0}」吗？删除后将无法恢复。", row.deviceName),
        this.$T("删除确认"),
        {
          confirmButtonText: this.$T("确定"),
          cancelButtonText: this.$T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          try {
            // 构造删除参数
            const deleteNode = {
              parentId: this.siteId, // 站点ID作为parentId
              ids: [row.id] // 设备ID数组
            };

            const response = await deleteDevice(deleteNode);

            if (response.code === 0) {
              this.$message.success(this.$T("删除设备成功"));

              // 重新加载设备列表
              this.loadDevices();

              // 通知父组件刷新树形结构
              this.$emit("refresh-tree");
            } else {
              this.$message.error(response.msg || this.$T("删除设备失败"));
            }
          } catch (error) {
            console.error("删除设备失败:", error);
            this.$message.error(this.$T("删除设备失败"));
          }
        })
        .catch(() => {
          this.$message.info(this.$T("已取消删除"));
        });
    },

    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },
    handleBatchDelete() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning("请选择要删除的设备");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedDevices.length} 个设备吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(async () => {
          try {
            const deviceIds = this.selectedDevices.map(device => device.id);
            console.log("批量删除设备IDs:", deviceIds);

            // 构造删除参数
            const deleteNode = {
              parentId: this.siteId, // 站点ID作为parentId
              ids: deviceIds // 设备ID数组
            };

            const response = await batchDeleteDevices(deleteNode);

            if (response.code === 0) {
              this.$message.success(
                this.$T("成功删除 {0} 个设备", this.selectedDevices.length)
              );

              // 清空选中状态
              this.selectedDevices = [];

              // 重新加载数据
              this.loadDevices();

              // 通知父组件刷新树形结构
              this.$emit("refresh-tree");
            } else {
              this.$message.error(response.msg || this.$T("批量删除设备失败"));
            }
          } catch (error) {
            console.error("批量删除设备失败:", error);
            this.$message.error(this.$T("批量删除设备失败"));
          }
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.loadDevices();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.loadDevices();
    },
    getStatusClass(status) {
      switch (status) {
        case "正常运行":
          return "status-normal";
        case "故障":
          return "status-error";
        case "维护中":
          return "status-maintenance";
        default:
          return "";
      }
    },

    // 加载站点信息
    async loadSiteInfo() {
      try {
        if (!this.siteId) {
          console.warn("站点ID为空，无法加载站点信息");
          return;
        }

        const res = await getSiteById(this.siteId);
        if (res.code === 0) {
          this.siteInfo = res.data;
          console.log("站点信息加载成功:", this.siteInfo);
        } else {
          console.error("加载站点信息失败:", res.msg);
          this.$message.error(res.msg || "加载站点信息失败");
        }
      } catch (error) {
        console.error("加载站点信息失败:", error);
        this.$message.error("加载站点信息失败");
      }
    },

    // 加载管网设备列表
    async loadNetworkDevices() {
      try {
        this.networkDeviceLoading = true;

        if (!this.siteInfo || !this.siteInfo.roomId) {
          this.networkDevices = [];
          this.networkDeviceTotal = 0;
          return;
        }

        const queryData = {
          roomId: this.siteInfo.roomId,
          roomType: this.siteInfo.roomType || "",
          siteId: this.siteId, // 添加站点ID
          deviceTypes: this.allowedNetworkDeviceTypes // 根据设备类型过滤管网设备类型
          // 暂时移除分页和搜索参数，先确保基本功能正常
          // page: this.networkDeviceCurrentPage,
          // size: this.networkDevicePageSize,
          // keyword: this.networkDeviceSearchKeyword
        };

        const res = await getMonitorDevicesByRoom(queryData);
        console.log("API返回的原始数据:", res);

        if (res.code === 0) {
          // 处理分组数据结构，将所有管网设备类型下的设备合并到一个数组中
          const allDevices = [];
          console.log("开始处理数据，res.data:", res.data);
          console.log("res.data类型:", typeof res.data);

          if (res.data && typeof res.data === "object") {
            const modelLabels = Object.keys(res.data);
            console.log("管网设备类型列表:", modelLabels);

            modelLabels.forEach(modelLabel => {
              const devicesInModel = res.data[modelLabel];
              console.log(
                `管网设备类型 ${modelLabel} 下的设备:`,
                devicesInModel
              );

              if (Array.isArray(devicesInModel)) {
                console.log(
                  `管网设备类型 ${modelLabel} 有 ${devicesInModel.length} 个设备`
                );
                devicesInModel.forEach((device, index) => {
                  console.log(`处理设备 ${index + 1}:`, device);
                  // 为每个设备添加管网设备类型信息，便于显示
                  const processedDevice = processDeviceData(device, modelLabel);
                  console.log(`处理后的设备:`, processedDevice);
                  allDevices.push(processedDevice);
                });
              } else {
                console.log(
                  `管网设备类型 ${modelLabel} 下的数据不是数组:`,
                  devicesInModel
                );
              }
            });
          } else {
            console.log("res.data 不是对象或为空");
          }

          console.log("最终处理的设备列表:", allDevices);
          console.log("设备总数:", allDevices.length);

          this.networkDevices = allDevices;
          this.networkDeviceTotal = allDevices.length;
          console.log("管网设备加载成功:", this.networkDevices);
        } else {
          console.error("加载管网设备失败:", res.msg);
          this.$message.error(res.msg || "加载管网设备失败");
          this.networkDevices = [];
          this.networkDeviceTotal = 0;
        }
      } catch (error) {
        console.error("加载管网设备失败:", error);
        this.$message.error("加载管网设备失败");
        this.networkDevices = [];
        this.networkDeviceTotal = 0;
      } finally {
        this.networkDeviceLoading = false;
      }
    },

    // 管网设备搜索
    handleNetworkDeviceSearch() {
      // 防抖处理
      clearTimeout(this.networkDeviceSearchTimer);
      this.networkDeviceSearchTimer = setTimeout(() => {
        this.networkDeviceCurrentPage = 1;
        this.loadNetworkDevices();
      }, 500);
    },

    // 管网设备分页大小改变
    handleNetworkDeviceSizeChange(size) {
      this.networkDevicePageSize = size;
      this.networkDeviceCurrentPage = 1;
      this.loadNetworkDevices();
    },

    // 管网设备当前页改变
    handleNetworkDeviceCurrentChange(page) {
      this.networkDeviceCurrentPage = page;
      this.loadNetworkDevices();
    },

    // 管网设备选择改变
    handleNetworkDeviceSelectionChange(selection) {
      this.selectedNetworkDevices = selection;
    },

    // 确认管网设备选择
    confirmNetworkDeviceSelection() {
      if (this.selectedNetworkDevices.length === 0) {
        this.$message.warning(this.$T("请选择管网设备"));
        return;
      }

      // 目前只支持单选，取第一个选中的设备
      const selectedDevice = this.selectedNetworkDevices[0];
      this.addDeviceFormData.networkDevice = selectedDevice.deviceName;
      this.addDeviceFormData.networkDeviceId = selectedDevice.id;

      // 保存管网设备的modelLabel，用于后续创建设备时传递
      this.addDeviceFormData.networkDeviceModelLabel =
        selectedDevice.modelLabel;

      console.log("选择的管网设备:", {
        id: selectedDevice.id,
        name: selectedDevice.deviceName,
        modelLabel: selectedDevice.modelLabel,
        deviceType: selectedDevice.deviceType
      });

      // 关闭弹窗
      this.networkDeviceDialogVisible = false;
      this.$message.success(this.$T("管网设备选择成功"));
    },

    // 加载电厂设备与管网设备关联关系
    async loadDeviceMonitorRelations() {
      try {
        const res = await getDeviceMonitorRelations();
        if (res.code === 0) {
          this.deviceMonitorRelations = res.data || [];
          console.log(
            "电厂设备与管网设备关联关系加载成功:",
            this.deviceMonitorRelations
          );
        } else {
          console.error("加载电厂设备与管网设备关联关系失败:", res.msg);
        }
      } catch (error) {
        console.error("加载电厂设备与管网设备关联关系失败:", error);
      }
    },

    // 设备类型改变处理
    handleDeviceTypeChange(deviceType) {
      console.log("设备类型改变:", deviceType);

      // 清空之前选择的管网设备
      this.addDeviceFormData.networkDevice = "";
      this.addDeviceFormData.networkDeviceId = "";

      // 根据设备类型获取允许关联的管网设备类型
      this.updateAllowedNetworkDeviceTypes(deviceType);
    },

    // 更新允许关联的管网设备类型
    updateAllowedNetworkDeviceTypes(deviceType) {
      this.allowedNetworkDeviceTypes = [];

      if (!deviceType || !this.deviceMonitorRelations.length) {
        return;
      }

      // deviceType现在已经是数字ID了，不需要转换
      const deviceTypeId = deviceType;
      if (!deviceTypeId) {
        console.warn("设备类型ID无效:", deviceType);
        return;
      }

      // 查找当前电厂设备类型对应的管网设备类型
      // 根据API文档，需要根据devicetype字段匹配，然后收集monitorlabel字段
      const monitorLabels = [];
      this.deviceMonitorRelations.forEach(relation => {
        // 使用设备类型ID进行匹配
        if (relation.devicetype === deviceTypeId) {
          monitorLabels.push(relation.monitorlabel);
        }
      });

      this.allowedNetworkDeviceTypes = [...new Set(monitorLabels)]; // 去重
      console.log("允许关联的管网设备类型:", this.allowedNetworkDeviceTypes);
    },

    // 判断设备类型是否匹配
    isDeviceTypeMatch(deviceTypeId, deviceTypeName) {
      // 根据设备类型名称找到对应的ID
      const deviceTypeOption = this.deviceTypeOptions.find(
        option => option.label === deviceTypeName
      );

      if (!deviceTypeOption) {
        return false;
      }

      // 比较ID是否匹配
      return deviceTypeOption.value === deviceTypeId;
    },

    // 加载设备类型选项
    loadDeviceTypeOptions() {
      this.deviceTypeOptions = getEnumOptions("VPP_DEVICE_TYPE");
      console.log("加载的设备类型选项:", this.deviceTypeOptions);
    },

    // 根据设备类型名称获取ID
    getDeviceTypeIdByName(deviceTypeName) {
      const option = this.deviceTypeOptions.find(
        opt => opt.label === deviceTypeName
      );
      return option ? option.value : null;
    },

    // 根据设备类型ID获取名称
    getDeviceTypeNameById(deviceTypeId) {
      return getEnumLabel("VPP_DEVICE_TYPE", deviceTypeId) || "未知类型";
    },

    // 根据管网设备类型标识获取中文名称
    getNetworkDeviceTypeName(modelLabel) {
      // 获取Network_Device_Type枚举选项
      const networkDeviceOptions = getEnumOptions("Network_Device_Type");
      // 根据propertyLabel查找对应的text
      const option = networkDeviceOptions.find(
        opt => opt.propertyLabel === modelLabel
      );
      return option ? option.text : modelLabel || "未知类型";
    }
  },
  created() {
    this.loadDeviceTypeOptions();
    this.loadDevices();
    this.loadDeviceMonitorRelations();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    if (this.networkDeviceSearchTimer) {
      clearTimeout(this.networkDeviceSearchTimer);
    }
  }
};
</script>
<style scoped>
.device-management-container {
  width: 100%;
  height: 100%;
}

.device-management {
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J2) var(--J3) var(--J3) var(--J3);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J2);
  margin-bottom: var(--J2);
}

.search-left {
  display: flex;
  align-items: center;
  gap: var(--J2);
  flex: 1;
}

.search-right {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.search-input {
  width: 240px;
  height: var(--J5);
}

.search-input .el-input__inner {
  height: var(--J5);
  line-height: var(--J5);
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.search-input .el-input__prefix {
  left: var(--J1);
}

.search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: var(--J5);
}

.category-select {
  width: 160px;
}

.category-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: var(--J5);
  line-height: var(--J5);
}

.category-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.category-select .el-input__suffix {
  right: 0;
}

.category-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

.add-device-btn {
  height: var(--J5);
  padding: 0 var(--J2);
  border: none;
  border-radius: var(--Ra);
  background: var(--ZS);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.add-device-btn:hover,
.add-device-btn:focus {
  background: var(--ZS);
  opacity: 0.8;
}

.batch-delete-btn {
  height: var(--J5);
  padding: 0 var(--J2);
  border: 1px solid var(--Sta3);
  border-radius: var(--Ra);
  background: var(--Sta3);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  margin-right: var(--J2);
}

.batch-delete-btn:hover,
.batch-delete-btn:focus {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.batch-delete-btn:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.batch-delete-btn:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}

.flex1 .el-table {
  background: transparent;
  border: none;
}

.flex1 .el-table th {
  background: var(--BG);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
}

.flex1 .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}

/* 设备状态样式 */
.status-normal {
  color: var(--Sta1);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-error {
  color: var(--Sta3);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-maintenance {
  color: var(--F2);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}
/* 操作链接样式 */
.action-link {
  font-size: var(--Aa);
  cursor: pointer;
  margin-right: var(--J2);
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-info {
  font-size: var(--Aa);
  color: var(--T1);
}

.total-info .total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.custom-pagination >>> .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.custom-pagination >>> .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.custom-pagination >>> .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.custom-pagination >>> .btn-prev,
.custom-pagination >>> .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.custom-pagination >>> .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.custom-pagination >>> .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* 新增设备弹窗样式 */
.add-device-dialog >>> .el-dialog {
  background: var(--BG1);
  border-radius: var(--Ra1);
  overflow: hidden;
}

.add-device-dialog >>> .el-dialog__header {
  background: var(--BG1);
  padding: var(--J3) var(--J4) var(--J2) var(--J4);
  position: relative;
}

.add-device-dialog >>> .el-dialog__title {
  color: var(--T1);
  font-size: var(--Ac);
  font-weight: 600;
}

.add-device-dialog >>> .el-dialog__headerbtn {
  position: absolute;
  top: var(--J3);
  right: var(--J3);
}

.add-device-dialog >>> .el-dialog__headerbtn .el-dialog__close {
  font-size: var(--Ac);
  color: var(--T3);
}

.add-device-dialog >>> .el-dialog__body {
  background: var(--BG1);
  padding: var(--J1) var(--J4) var(--J3) var(--J4);
}

.add-device-dialog >>> .el-dialog__footer {
  background: var(--BG1);
  padding: var(--J2) var(--J4) var(--J3) var(--J4);
  text-align: right;
}

.dialog-content {
  background: var(--BG1);
}

.device-form >>> .el-form-item__label {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 22px;
  padding-bottom: 8px;
}

.device-form >>> .el-form-item__label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.device-form >>> .el-input__inner {
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #333;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
}

.device-form >>> .el-input__inner:focus {
  border-color: #409eff;
}

.device-form >>> .el-input__inner::placeholder {
  color: #c0c4cc;
}

.device-form >>> .el-select .el-input__inner {
  cursor: pointer;
}

.network-device-input {
  position: relative;
}

.network-device-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  z-index: 10;
}

/* 管网设备选择弹窗样式 */
.network-device-dialog >>> .el-dialog__wrapper {
  z-index: 3000 !important;
}

.network-device-dialog .el-dialog__body {
  padding: 20px;
}

.network-device-content {
  width: 100%;
}

.network-device-content .pagination-container {
  margin-top: 16px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  font-weight: 400;
  border-radius: 4px;
  padding: 9px 20px;
  font-size: 14px;
  height: 40px;
  line-height: 20px;
}

.dialog-footer .el-button--default {
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.dialog-footer .el-button--default:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.dialog-footer .el-button--success {
  background: #00b45e;
  border: 1px solid #00b45e;
  color: #fff;
}

.dialog-footer .el-button--success:hover {
  background: #00c870;
  border-color: #00c870;
}

/* 设备详情抽屉样式 */
.device-detail-drawer >>> .el-drawer {
  background: #fff;
}

.device-detail-drawer >>> .el-drawer__header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 20px 24px;
  margin-bottom: 0;
}

.device-detail-drawer >>> .el-drawer__title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}

.device-detail-drawer >>> .el-drawer__close-btn {
  color: #909399;
  font-size: 20px;
}

.device-detail-drawer >>> .el-drawer__body {
  background: #fff;
  padding: 0;
}

.detail-content {
  padding: 24px;
  background: #fff;
}

/* 设备基本信息样式 */
.basic-info-section {
  margin-bottom: 32px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  color: #666;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  margin-bottom: 8px;
  line-height: 20px;
}

.info-value {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 20px;
  min-height: 20px;
}

/* 管网设备样式 */
.network-device-section {
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
}

.network-device-section .info-label {
  margin-bottom: 8px;
}

.network-device-section .info-value {
  color: #333;
}

/* 管网设备列表样式 */
.device-list-section {
  margin-bottom: 24px;
}

.list-title {
  color: #333;
  font-size: 16px;
  font-weight: 500;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  margin-bottom: 16px;
  line-height: 24px;
}

.device-list-table {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e6e6e6;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  background: #fff;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 16px;
  color: #333;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  line-height: 20px;
}

.header-cell {
  padding: 12px 16px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  background: #f8f9fa;
  line-height: 20px;
}

.serial-cell {
  flex: 0 0 120px;
  border-right: 1px solid #e6e6e6;
}

.name-cell {
  flex: 1;
  border-right: 1px solid #e6e6e6;
}

.type-cell {
  flex: 0 0 150px;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  background: #fff;
}

.empty-text {
  color: #999;
  font-size: 14px;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}
</style>

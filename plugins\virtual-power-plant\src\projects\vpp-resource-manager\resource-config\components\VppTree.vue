<template>
  <div class="vpp-tree">
    <!-- 加载状态 -->
    <div v-if="loading" class="tree-loading">
      <i class="el-icon-loading"></i>
      <span>加载中...</span>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="tree-error">
      <i class="el-icon-warning"></i>
      <span>{{ error }}</span>
      <el-button size="mini" type="text" @click="loadTreeData">重试</el-button>
    </div>

    <!-- 树形组件容器 -->
    <div v-else class="tree-container">
      <CetGiantTree
        v-bind="CetGiantTree_config"
        v-on="CetGiantTree_config.event"
        class="vpp-giant-tree"
      />
    </div>

    <!-- 右侧滚动条指示器 -->
    <div class="scroll-indicator" v-if="showScrollIndicator">
      <div class="scroll-thumb"></div>
    </div>
  </div>
</template>

<script>
import {
  getAllVppTrees,
  transformTreeData,
  flattenTreeData,
  refreshVppTreeCache
} from "@/api/vpp-tree-cache";

export default {
  name: "VppTree",
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    vppId: {
      type: Number,
      default: null
    },
    autoLoad: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showScrollIndicator: false,
      loading: false,
      error: null,
      isLoadingFromApi: false, // 标记是否正在从API加载数据，避免循环调用
      hasLoadedOnce: false, // 标记是否已经加载过一次，避免重复加载
      CetGiantTree_config: {
        inputData_in: [],
        checkedNodes: [],
        selectNode: {},
        setting: {
          check: {
            enable: false // 单选模式
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id",
              pIdKey: "pId",
              rootPId: null
            }
          },
          view: {}
        },
        event: {
          created_out: this.onTreeCreated,
          currentNode_out: this.onNodeClick,
          checkedNodes_out: this.onNodesChecked
        }
      }
    };
  },
  watch: {
    treeData: {
      handler(newData) {
        // 如果正在从API加载数据，忽略treeData的变化，避免循环调用
        if (this.isLoadingFromApi) {
          return;
        }

        if (newData && newData.length > 0) {
          this.initTreeData();
        }
        // 移除这里的自动加载逻辑，避免与mounted重复
      },
      immediate: true
    },
    vppId: {
      handler(newVppId) {
        if (newVppId && this.autoLoad && !this.isLoadingFromApi) {
          this.loadTreeData();
        }
      }
    }
  },
  mounted() {
    this.showScrollIndicator = true; // 根据SVG设计稿显示滚动指示器

    // 只在mounted中进行一次自动加载检查，且确保没有加载过
    if (
      this.autoLoad &&
      (!this.treeData || this.treeData.length === 0) &&
      !this.hasLoadedOnce
    ) {
      this.loadTreeData();
    }
  },
  methods: {
    /**
     * 从API加载树形数据
     */
    async loadTreeData() {
      // 如果已经在加载中或已经加载过，直接返回
      if (this.isLoadingFromApi || this.hasLoadedOnce) {
        return;
      }

      this.loading = true;
      this.error = null;
      this.isLoadingFromApi = true; // 设置API加载标志，避免循环调用
      this.hasLoadedOnce = true; // 标记已经加载过

      try {
        let response;
        // 加载所有VPP树
        response = await getAllVppTrees();

        if (response && response.code === 0 && response.data) {
          // 转换API数据格式为组件需要的格式
          const transformedData = transformTreeData(response.data);

          // 扁平化为zTree格式
          const flattenedData = flattenTreeData(transformedData);

          this.CetGiantTree_config.inputData_in = flattenedData;
          this.$emit("tree-loaded", flattenedData);

          // 延迟触发tree-data-updated事件，确保isLoadingFromApi标志已重置
          this.$nextTick(() => {
            this.isLoadingFromApi = false; // 重置API加载标志
            this.$emit("tree-data-updated", flattenedData);
            // 选中第一个节点
            this.selectFirstNode(flattenedData);
          });
        } else {
          const errorMsg = response?.msg || "加载树形数据失败";
          this.error = errorMsg;
          console.error("❌ 加载树形数据失败:", {
            response,
            code: response?.code,
            msg: response?.msg,
            data: response?.data
          });
          this.CetGiantTree_config.inputData_in = [];
          this.isLoadingFromApi = false; // 重置API加载标志
        }
      } catch (error) {
        console.error("💥 加载树形数据异常:", error);
        console.error("错误详情:", {
          message: error.message,
          stack: error.stack,
          response: error.response
        });

        // 统一的错误处理
        this.error = `网络错误，加载树形数据失败: ${error.message}`;
        this.CetGiantTree_config.inputData_in = [];
        this.isLoadingFromApi = false; // 重置API加载标志
        this.hasLoadedOnce = false; // 重置加载标记，允许重试
      } finally {
        this.loading = false;
        this.isLoadingFromApi = false; // 确保在finally中重置API加载标志
      }
    },

    /**
     * 刷新树形数据
     */
    async refreshTreeData() {
      if (this.vppId) {
        try {
          await refreshVppTreeCache(this.vppId);
        } catch (error) {
          console.error("刷新缓存失败:", error);
        }
      }

      // 重置标志位，确保可以重新加载数据
      this.isLoadingFromApi = false;
      this.hasLoadedOnce = false; // 重置加载标记，允许重新加载
      await this.loadTreeData();
    },

    /**
     * 初始化树形数据（仅处理外部传入的数据）
     */
    initTreeData() {
      // 只使用props传入的treeData
      if (this.treeData && this.treeData.length > 0) {
        this.CetGiantTree_config.inputData_in = this.treeData;
        this.selectFirstNode(this.treeData);
      } else {
        // 如果没有外部数据且未启用自动加载，显示空状态
        this.CetGiantTree_config.inputData_in = [];
        this.error = "暂无数据";
      }
    },

    /**
     * 选中第一个节点的通用方法
     */
    selectFirstNode(data) {
      this.$nextTick(() => {
        if (data && data.length > 0) {
          this.CetGiantTree_config.selectNode = data[0];
          // 手动触发节点点击事件
          this.onNodeClick(data[0]);
        }
      });
    },

    /**
     * 树创建完成事件
     */
    onTreeCreated(treeObj) {
      this.treeObj = treeObj;
    },

    /**
     * 节点点击事件
     */
    onNodeClick(node) {
      this.$emit("node-click", node);
    },

    /**
     * 节点勾选事件
     */
    onNodesChecked(nodes) {
      this.$emit("nodes-checked", nodes);
    }
  }
};
</script>

<style lang="scss" scoped>
.vpp-tree {
  width: 312px;
  height: 794px;
  position: relative;
  display: flex;
  flex-direction: column;
  border: none;

  .tree-loading,
  .tree-error {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--T3);
    font-size: 14px;

    i {
      font-size: 24px;
      margin-bottom: var(--J2);
      color: var(--T4);
    }

    span {
      margin-bottom: var(--J2);
      text-align: center;
    }
  }

  .tree-error {
    i {
      color: var(--danger);
    }
  }

  .tree-container {
    flex: 1;
    overflow: hidden;

    .vpp-giant-tree {
      height: 100%;
    }
  }

  // 为搜索框添加内边距，防止超出容器
  :deep(.gianttree .device-search) {
    margin: 0 var(--J2) var(--J2) var(--J2) !important;
  }

  // 为树形结构添加左右内边距
  :deep(.gianttree .ztree) {
    padding-left: var(--J2);
    padding-right: var(--J2);
  }

  // 右侧滚动条指示器 - 根据SVG设计稿精确匹配
  .scroll-indicator {
    position: absolute;
    right: 0;
    top: 280px;
    width: 12px;
    height: 64px;
    background: var(--Sta1);
    border-radius: var(--Ra);
    z-index: 10;

    .scroll-thumb {
      position: absolute;
      top: 29px;
      left: 3px;
      width: 6px;
      height: 6px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12"><path fill="white" d="M7.25 9.48C7.38 9.48 7.5 9.53 7.6 9.63C7.8 9.83 7.8 10.13 7.6 10.33L4.95 12.98L7.6 15.63C7.8 15.83 7.8 16.13 7.6 16.33C7.4 16.53 7.1 16.53 6.9 16.33L3.9 13.33C3.7 13.13 3.7 12.83 3.9 12.63L6.9 9.63C7 9.53 7.13 9.48 7.25 9.48Z" transform="rotate(90 6 13)"/></svg>')
        no-repeat center;
      background-size: contain;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .vpp-tree {
    width: 100%;
    height: auto;

    .search-container {
      .tree-search-input {
        width: 100%;
      }
    }
  }
}
</style>

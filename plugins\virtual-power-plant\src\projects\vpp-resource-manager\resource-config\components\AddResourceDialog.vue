<template>
  <el-dialog
    :title="$T('新增资源')"
    :visible.sync="dialogVisible"
    width="1000px"
    :destroy-on-close="true"
    class="add-resource-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="addResourceForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="top"
      >
        <div class="form-container">
          <el-row :gutter="20">
            <!-- 电户号 -->
            <el-col :span="8">
              <el-form-item prop="electricityUserNumbers">
                <span slot="label" class="required-label">
                  {{ $T("电户号") }}
                </span>
                <el-input
                  v-model="formData.electricityUserNumbers"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 资源名称 -->
            <el-col :span="8">
              <el-form-item prop="resourceName">
                <span slot="label" class="required-label">
                  {{ $T("资源名称") }}
                </span>
                <el-input
                  v-model="formData.resourceName"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 报装容量 -->
            <el-col :span="8">
              <el-form-item prop="registeredCapacity">
                <span slot="label" class="required-label">
                  {{ $T("报装容量") }}
                </span>
                <el-input
                  v-model="formData.registeredCapacity"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kVA</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 平台直控 -->
            <el-col :span="8">
              <el-form-item prop="platformDirectControl">
                <span slot="label" class="required-label">
                  {{ $T("平台直控") }}
                </span>
                <el-select
                  v-model="formData.platformDirectControl"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in platformDirectControlOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 资源类型 -->
            <el-col :span="8">
              <el-form-item prop="resourceType">
                <span slot="label" class="required-label">
                  {{ $T("资源类型") }}
                </span>
                <el-select
                  v-model="formData.resourceType"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in resourceTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 响应方式 -->
            <el-col :span="8">
              <el-form-item prop="responseMode">
                <span slot="label" class="required-label">
                  {{ $T("响应方式") }}
                </span>
                <el-select
                  v-model="formData.responseMode"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in responseModeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 资源状态 -->
            <el-col :span="8">
              <el-form-item prop="resourceStatus">
                <span slot="label" class="required-label">
                  {{ $T("资源状态") }}
                </span>
                <el-select
                  v-model="formData.resourceStatus"
                  :placeholder="$T('请选择')"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in resourceStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 最大可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="maxRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最大可运行功率") }}
                </span>
                <el-input
                  v-model="formData.maxRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最小可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="minRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最小可运行功率") }}
                </span>
                <el-input
                  v-model="formData.minRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 最大上调速率 -->
            <el-col :span="6">
              <el-form-item prop="maxUpRate">
                <span slot="label">{{ $T("最大上调速率") }}</span>
                <el-input
                  v-model="formData.maxUpRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大下调速率 -->
            <el-col :span="6">
              <el-form-item prop="maxDownRate">
                <span slot="label">{{ $T("最大下调速率") }}</span>
                <el-input
                  v-model="formData.maxDownRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 经度 -->
            <el-col :span="6">
              <el-form-item prop="longitude">
                <span slot="label">{{ $T("经度") }}</span>
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请选择')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 纬度 -->
            <el-col :span="6">
              <el-form-item prop="latitude">
                <span slot="label">{{ $T("纬度") }}</span>
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请选择')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 联系人 -->
            <el-col :span="8">
              <el-form-item prop="contactPerson">
                <span slot="label">{{ $T("联系人") }}</span>
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系电话 -->
            <el-col :span="8">
              <el-form-item prop="contactPhone">
                <span slot="label">{{ $T("联系电话") }}</span>
                <el-input
                  v-model="formData.contactPhone"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 区域 -->
            <el-col :span="8">
              <el-form-item prop="region">
                <span slot="label" class="required-label">
                  {{ $T("区域") }}
                </span>
                <el-cascader
                  v-model="formData.region"
                  :options="regionOptions"
                  :placeholder="$T('请选择区域')"
                  size="small"
                  style="width: 240px"
                  clearable
                  :props="{ value: 'code', label: 'name' }"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 地址 -->
            <el-col :span="24">
              <el-form-item prop="address">
                <span slot="label">{{ $T("地址") }}</span>
                <el-input
                  v-model="formData.address"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $T("确定") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getEnumOptions } from "../../../../utils/enumManager";
import { getGeographicalData } from "@/api/base-config";

export default {
  name: "AddResourceDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    province: {
      type: Number,
      default: null
    },
    city: {
      type: Number,
      default: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },
    // 从 enumManager 获取资源类型选项
    resourceTypeOptions() {
      return getEnumOptions("RESOURCE_TYPE");
    },
    // 从 enumManager 获取响应方式选项
    responseModeOptions() {
      return getEnumOptions("RESPONSE_MODE");
    },
    // 从 enumManager 获取资源状态选项
    resourceStatusOptions() {
      return getEnumOptions("RESOURCE_STATUS_CODES");
    }
  },
  data() {
    return {
      formData: {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        resourceStatus: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        region: [],
        address: ""
      },
      formRules: {
        electricityUserNumbers: [
          {
            required: true,
            message: this.$T("电户号不能为空"),
            trigger: "blur"
          }
        ],
        resourceName: [
          {
            required: true,
            message: this.$T("资源名称不能为空"),
            trigger: "blur"
          }
        ],
        registeredCapacity: [
          {
            required: true,
            message: this.$T("报装容量不能为空"),
            trigger: "blur"
          }
        ]
      },
      // 平台直控选项
      platformDirectControlOptions: [
        { label: "是", value: true },
        { label: "否", value: false }
      ],

      // 区域选项
      regionOptions: []
    };
  },
  watch: {
    // 监听省份变化，重新加载地理数据
    province: {
      handler() {
        this.loadGeographicalData();
      }
    },
    // 监听城市变化，重新加载地理数据
    city: {
      handler() {
        this.loadGeographicalData();
      }
    }
  },
  mounted() {
    this.loadGeographicalData();
  },
  methods: {
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0) {
          this.regionOptions = this.transformGeographicalData(
            response.data,
            this.province,
            this.city
          );
        } else {
          this.$message.error(this.$T("加载地理数据失败"));
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error(this.$T("加载地理数据失败"));
      }
    },

    // 转换地理数据为级联选择器格式
    transformGeographicalData(data, provinceId, cityId) {
      if (!data) return [];
      let filteredData = data;
      if (provinceId) {
        filteredData = filteredData.filter(p => p.code === provinceId);
      }
      if (cityId) {
        filteredData.forEach(p => {
          if (p.children) {
            p.children = p.children.filter(c => c.code === cityId);
          }
        });
      }
      return filteredData;
    },
    handleClose() {
      // 重置表单
      this.resetForm();
      this.$emit("close");
    },

    // 重置表单数据
    resetForm() {
      // 使用Element UI的resetFields方法
      this.$refs.addResourceForm?.resetFields();

      // 手动重置表单数据，确保完全清空
      this.formData = {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        resourceStatus: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        region: [],
        address: ""
      };
    },
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.addResourceForm.validate();

        // 提交数据
        const [provinceId, cityId, districtId] = this.formData.region || [];
        const formData = {
          ...this.formData,
          province: provinceId,
          city: cityId,
          district: districtId
        };

        // 移除临时的 region 字段，避免传递给后端
        delete formData.region;
        this.$emit("save", formData);

        // 注意：成功消息和关闭弹窗的操作现在由父组件处理
        // 这样可以确保只有在API调用成功后才关闭弹窗和显示成功消息
      } catch (error) {
        console.error("表单验证失败:", error);
        this.$message.error(this.$T("请检查表单输入"));
      }
    }
  }
};
</script>

<style scoped>
.form-container {
  padding: 20px 0;
}

.required-label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>

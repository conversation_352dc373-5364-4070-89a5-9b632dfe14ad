# 地理位置数据使用指南

## 概述

本指南介绍如何在Vue组件中使用geographical store来获取和使用地理位置数据（省市区三级联动）。

## Store结构

### State
```javascript
{
  geographicalData: null  // 地理数据（省市区三级嵌套结构）
}
```

### 数据格式
```javascript
[
  {
    "children": [
      {
        "children": [...],           // 区县列表
        "modelLabel": "city",
        "code": 110100,
        "name": "北京市"
      }
    ],
    "modelLabel": "province",
    "code": 110000,
    "name": "北京市"
  }
]
```

## 使用方法

### 1. 在组件中引入

```javascript
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  computed: {
    ...mapState('geographical', ['geographicalData']),
    ...mapGetters('geographical', [
      'provinces',
      'getCitiesByProvince',
      'getDistrictsByCity',
      'getAreaNameByCode'
    ])
  },
  methods: {
    ...mapActions('geographical', ['setGeographicalData'])
  }
}
```

### 2. 加载地理数据

```javascript
// 在组件创建时加载数据
async created() {
  await this.setGeographicalData();
}

// 强制刷新数据
async refreshData() {
  await this.setGeographicalData(true);
}
```

### 3. 三级联动选择器示例

```vue
<template>
  <div>
    <!-- 省份选择 -->
    <el-select v-model="selectedProvince" @change="handleProvinceChange">
      <el-option
        v-for="province in provinces"
        :key="province.code"
        :label="province.name"
        :value="province.code"
      />
    </el-select>

    <!-- 城市选择 -->
    <el-select v-model="selectedCity" @change="handleCityChange" :disabled="!selectedProvince">
      <el-option
        v-for="city in cities"
        :key="city.code"
        :label="city.name"
        :value="city.code"
      />
    </el-select>

    <!-- 区县选择 -->
    <el-select v-model="selectedDistrict" :disabled="!selectedCity">
      <el-option
        v-for="district in districts"
        :key="district.code"
        :label="district.name"
        :value="district.code"
      />
    </el-select>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';

export default {
  data() {
    return {
      selectedProvince: '',
      selectedCity: '',
      selectedDistrict: ''
    };
  },
  computed: {
    ...mapGetters('geographical', [
      'provinces',
      'getCitiesByProvince',
      'getDistrictsByCity'
    ]),
    cities() {
      return this.selectedProvince ? this.getCitiesByProvince(this.selectedProvince) : [];
    },
    districts() {
      return this.selectedCity ? this.getDistrictsByCity(this.selectedCity) : [];
    }
  },
  async created() {
    await this.setGeographicalData();
  },
  methods: {
    ...mapActions('geographical', ['setGeographicalData']),
    handleProvinceChange() {
      this.selectedCity = '';
      this.selectedDistrict = '';
    },
    handleCityChange() {
      this.selectedDistrict = '';
    }
  }
};
</script>
```

### 4. 级联选择器示例

```vue
<template>
  <el-cascader
    v-model="selectedArea"
    :options="cascaderOptions"
    :props="cascaderProps"
    placeholder="请选择省市区"
  />
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      selectedArea: [],
      cascaderProps: {
        value: 'code',
        label: 'name',
        children: 'children'
      }
    };
  },
  computed: {
    ...mapGetters('geographical', ['provinces']),
    cascaderOptions() {
      return this.provinces.map(province => ({
        code: province.code,
        name: province.name,
        children: (province.children || []).map(city => ({
          code: city.code,
          name: city.name,
          children: (city.children || []).map(district => ({
            code: district.code,
            name: district.name
          }))
        }))
      }));
    }
  }
};
</script>
```

## API参考

### Actions
- `setGeographicalData(forceRefresh = false)` - 获取并设置地理位置数据

### Getters
- `provinces` - 获取所有省份列表
- `getCitiesByProvince(provinceCode)` - 根据省份编码获取城市列表
- `getDistrictsByCity(cityCode)` - 根据城市编码获取区县列表
- `getAreaNameByCode(code)` - 根据编码获取地区名称

## 注意事项

1. 地理数据具有缓存机制，默认缓存30分钟
2. 使用 `forceRefresh = true` 可以强制刷新缓存
3. 所有getter都会自动处理数据为空的情况
4. 建议在应用启动时预加载地理数据以提升用户体验n": [
          {
            "children": null,
            "modelLabel": "district",
            "code": 110101,
            "name": "东城区"
          }
        ],
        "modelLabel": "city",
        "code": 110100,
        "name": "北京市"
      }
    ],
    "modelLabel": "province",
    "code": 110000,
    "name": "北京市"
  }
]
```

## 在组件中使用

### 1. 引入Vuex辅助函数

```javascript
import { mapActions, mapGetters } from "vuex";
```

### 2. 映射getters和actions

```javascript
export default {
  computed: {
    ...mapGetters("baseConfig", ["geographicalData"]),
    
    // 省份选项
    provinceOptions() {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return [];
      }
      return this.geographicalData.map(province => ({
        code: province.code,
        name: province.name
      }));
    }
  },
  
  methods: {
    ...mapActions("baseConfig", ["loadGeographicalData"]),
    
    // 初始化地理数据
    async initGeographicalData() {
      try {
        await this.loadGeographicalData();
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error("加载省份数据失败");
      }
    }
  }
}
```

### 3. 获取城市和区县数据

```javascript
computed: {
  // 根据选中的省份获取城市列表
  cityOptions() {
    if (!this.selectedProvinceCode) return [];
    
    const cities = this.$store.getters["baseConfig/getCitiesByProvinceCode"](this.selectedProvinceCode);
    return cities.map(city => ({
      code: city.code,
      name: city.name
    }));
  },
  
  // 根据选中的城市获取区县列表
  districtOptions() {
    if (!this.selectedCityCode) return [];
    
    const districts = this.$store.getters["baseConfig/getDistrictsByCityCode"](this.selectedCityCode);
    return districts.map(district => ({
      code: district.code,
      name: district.name
    }));
  }
}
```

### 4. 在模板中使用

```vue
<template>
  <!-- 省份选择 -->
  <el-select v-model="form.province" placeholder="请选择省份">
    <el-option
      v-for="province in provinceOptions"
      :key="province.code"
      :label="province.name"
      :value="province.code"
    ></el-option>
  </el-select>
  
  <!-- 城市选择 -->
  <el-select v-model="form.city" placeholder="请选择城市">
    <el-option
      v-for="city in cityOptions"
      :key="city.code"
      :label="city.name"
      :value="city.code"
    ></el-option>
  </el-select>
  
  <!-- 区县选择 -->
  <el-select v-model="form.district" placeholder="请选择区县">
    <el-option
      v-for="district in districtOptions"
      :key="district.code"
      :label="district.name"
      :value="district.code"
    ></el-option>
  </el-select>
</template>
```

## 完整示例

参考 `src/projects/vpp-resource-manager/virtual-power-plant-config/components/ConfigDialog.vue` 文件，该文件已经按照最佳实践进行了更新。

## 最佳实践

1. **数据加载时机**：在组件mounted时或对话框打开时加载数据
2. **错误处理**：使用try-catch处理加载失败的情况
3. **数据缓存**：store会自动缓存数据，避免重复请求
4. **响应式更新**：使用computed属性确保数据变化时UI自动更新

## 注意事项

1. 确保在使用前先调用`loadGeographicalData()`加载数据
2. 数据结构为三级嵌套，使用对应的getter方法获取子级数据
3. 所有的code都是数字类型，name是字符串类型
4. 如果数据未加载或加载失败，相关的computed属性会返回空数组

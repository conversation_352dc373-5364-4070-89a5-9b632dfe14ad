<template>
  <el-dialog
    :title="$T('配置虚拟电厂')"
    :visible.sync="dialogVisible"
    width="880px"
    @close="handleClose"
    class="config-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂名称')" prop="vppName">
              <el-input
                v-model="form.vppName"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂所属省份')" prop="province">
              <el-select
                v-model="form.province"
                :placeholder="$T('请选择省份')"
                class="custom-input"
              >
                <el-option
                  v-for="province in provinceOptions"
                  :key="province.code"
                  :label="province.name"
                  :value="province.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂场站类型')" prop="vppType">
              <el-select
                v-model="form.vppType"
                :placeholder="$T('请选择类型')"
                class="custom-input"
              >
                <el-option :label="$T('聚合型')" value="1"></el-option>
                <el-option :label="$T('协调型')" value="2"></el-option>
                <el-option :label="$T('混合型')" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂成立日期')" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="date"
                :placeholder="$T('请选择日期')"
                class="custom-input"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('运营商编号')" prop="operatorcode">
              <el-input
                v-model="form.operatorcode"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$T('虚拟电厂描述')">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.description"
            :placeholder="$T('请输入虚拟电厂描述')"
            class="station-desc-input"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$T('上传图片')">
          <el-upload
            action="#"
            list-type="picture-card"
            :file-list="fileList"
            :before-upload="handleImageUpload"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            accept="image/png,image/jpg,image/jpeg"
            class="custom-upload"
          ></el-upload>
          <p class="upload-tip">
            {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
          </p>
        </el-form-item>

        <!-- 需求响应 -->
        <h4>{{ $T("需求响应") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="demandResponseDeclaredPriceCaps"
            >
              <el-input
                v-model="form.demandResponseDeclaredPriceCaps"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="demandResponseFilingPriceCaps"
            >
              <el-input
                v-model="form.demandResponseFilingPriceCaps"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调峰 -->
        <h4>{{ $T("调峰") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="peakingDeclaredPriceFloor"
            >
              <el-input
                v-model="form.peakingDeclaredPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="peakingFilingPriceFloor"
            >
              <el-input
                v-model="form.peakingFilingPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调频 -->
        <h4>{{ $T("调频") }}</h4>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="pmDeclaredPriceFloor"
            >
              <el-input
                v-model="form.pmDeclaredPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$T('申报价格下限')" prop="pmFilingPriceFloor">
              <el-input
                v-model="form.pmFilingPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调节 - 仅当类型为调节型时显示 -->
        <div v-if="form.vppType === '3'">
          <h4>{{ $T("调节") }}</h4>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item
                :label="$T('申报价格上限')"
                prop="adjustDeclaredPriceFloor"
              >
                <el-input
                  v-model="form.adjustDeclaredPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('申报价格下限')"
                prop="adjustFilingPriceFloor"
              >
                <el-input
                  v-model="form.adjustFilingPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 能量 - 仅当类型为容量型时显示 -->
        <div v-if="form.vppType === '4'">
          <h4>{{ $T("能量") }}</h4>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item
                :label="$T('申报价格上限')"
                prop="energyResponseDeclaredPriceFloor"
              >
                <el-input
                  v-model="form.energyResponseDeclaredPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="$T('申报价格下限')"
                prop="energyResponseFilingPriceFloor"
              >
                <el-input
                  v-model="form.energyResponseFilingPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </span>

    <el-dialog :visible.sync="imagePreviewVisible">
      <img width="100%" :src="imagePreviewUrl" alt="" />
    </el-dialog>
  </el-dialog>
</template>

<script>
import { uploadImage } from "@/api/base-config";

export default {
  name: "ConfigDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    vppData: {
      type: Object,
      default: () => ({})
    },
    provinceOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: null,
        vppName: "",
        province: null,
        vppType: null,
        description: "",
        operatorcode: "",
        imageUrl: "", // 图片路径
        demandResponseFilingPriceCaps: null,
        demandResponseDeclaredPriceCaps: null,
        peakingDeclaredPriceFloor: null,
        peakingFilingPriceFloor: null,
        pmDeclaredPriceFloor: null,
        pmFilingPriceFloor: null,
        adjustDeclaredPriceFloor: null,
        adjustFilingPriceFloor: null,
        energyResponseDeclaredPriceFloor: null,
        energyResponseFilingPriceFloor: null,
        createTime: null
      },
      rules: {
        vppName: [
          { required: true, message: "请输入虚拟电厂名称", trigger: "blur" }
        ],
        province: [
          { required: true, message: "请选择所属省份", trigger: "change" }
        ],
        vppType: [
          { required: true, message: "请选择场站类型", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "请选择成立日期", trigger: "change" }
        ],
        operatorcode: [
          { required: true, message: "请输入运营商编号", trigger: "blur" }
        ]
      },
      fileList: [],
      imagePreviewVisible: false,
      imagePreviewUrl: ""
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val && this.vppData) {
          // 只保留form中定义的字段
          this.form = {
            id: this.vppData.id || null,
            vppName: this.vppData.vppName || "",
            province: this.vppData.province || null,
            vppType: this.vppData.vppType || null,
            description: this.vppData.description || "",
            operatorcode: this.vppData.operatorcode || "",
            imageUrl: this.vppData.imageUrl || "",
            demandResponseFilingPriceCaps:
              this.vppData.demandResponseFilingPriceCaps || null,
            demandResponseDeclaredPriceCaps:
              this.vppData.demandResponseDeclaredPriceCaps || null,
            peakingDeclaredPriceFloor:
              this.vppData.peakingDeclaredPriceFloor || null,
            peakingFilingPriceFloor:
              this.vppData.peakingFilingPriceFloor || null,
            pmDeclaredPriceFloor: this.vppData.pmDeclaredPriceFloor || null,
            pmFilingPriceFloor: this.vppData.pmFilingPriceFloor || null,
            adjustDeclaredPriceFloor:
              this.vppData.adjustDeclaredPriceFloor || null,
            adjustFilingPriceFloor: this.vppData.adjustFilingPriceFloor || null,
            energyResponseDeclaredPriceFloor:
              this.vppData.energyResponseDeclaredPriceFloor || null,
            energyResponseFilingPriceFloor:
              this.vppData.energyResponseFilingPriceFloor || null,
            createTime: this.vppData.createTime || null
          };
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    },
    // 监听图片URL变化，更新文件列表显示
    "form.imageUrl": {
      handler(newVal) {
        if (newVal) {
          this.fileList = [
            {
              name: "image",
              url: `/vpp/api/v1/resource-manager/base-config/images/download/${newVal}`
            }
          ];
        } else {
          this.fileList = [];
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 提交时转换时间格式
          const formData = {
            ...this.form
          };
          this.$emit("confirm", formData);
          this.handleClose();
        }
      });
    },

    // 图片上传前处理
    async handleImageUpload(file) {
      // 文件类型检查
      const isImage = /^image\/(png|jpe?g)$/i.test(file.type);
      if (!isImage) {
        this.$message.error(this.$T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }

      // 文件大小检查（1MB）
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isLt1M) {
        this.$message.error(this.$T("图片大小不能超过1MB"));
        return false;
      }

      try {
        // 调用上传接口
        const response = await uploadImage(file);

        if (response.code === 0) {
          // 保存图片路径
          this.form.imageUrl = response.data.imagePath;

          this.$message.success(this.$T("图片上传成功"));
        } else {
          this.$message.error(response.msg || this.$T("图片上传失败"));
        }
      } catch (error) {
        console.error("图片上传失败:", error);
        this.$message.error(this.$T("图片上传失败，请稍后重试"));
      }

      return false; // 阻止自动上传
    },

    // 预览图片
    handlePreview(file) {
      this.imagePreviewUrl = file.url;
      this.imagePreviewVisible = true;
    },

    // 删除图片
    handleRemove() {
      this.form.imageUrl = "";
      this.fileList = [];
    }
  }
};
</script>

<style scoped lang="scss">
.config-dialog {
  :deep(.el-dialog__body) {
    max-height: 600px;
  }

  .dialog-content {
    max-height: 576px;
    overflow-y: auto;
  }
}
h4 {
  margin: 0;
}
.upload-tip {
  color: var(--T4);
  font-size: var(--Ab);
  margin: 0;
}
.custom-input {
  width: 256px;
}

.price-input {
  width: 282px;
}

.custom-upload {
  height: 80px;
  width: 80px;
  :deep(.el-upload--picture-card) {
    width: 100%;
    height: 100%;
  }
}

.station-desc-input {
  :deep(.el-textarea__inner) {
    background-color: var(--BG);
  }
}
</style>
